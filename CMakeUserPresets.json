{"version": 3, "configurePresets": [{"name": "Qt-Debug", "inherits": "Qt-<PERSON><PERSON><PERSON>", "binaryDir": "${sourceDir}/out/build/debug", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_CXX_FLAGS": "-DQT_QML_DEBUG"}, "environment": {"QML_DEBUG_ARGS": "-qmljsdebugger=file:{39133953-fb8e-4161-84c2-513972b5e0fd},block"}}, {"name": "Qt-Release", "inherits": "Qt-<PERSON><PERSON><PERSON>", "binaryDir": "${sourceDir}/out/build/release", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"hidden": true, "name": "Qt-<PERSON><PERSON><PERSON>", "inherits": "5.14.2_msvc2017_64", "vendor": {"qt-project.org/Default": {"checksum": "qwjTNEov+Vp+pUKkZgUK4KP5rds="}}}, {"hidden": true, "name": "5.14.2_msvc2017_64", "inherits": "Qt", "environment": {"QTDIR": "D:/Software/Qt/install/Qt5.14.2/5.14.2/msvc2017_64"}, "architecture": {"strategy": "external", "value": "x64"}, "generator": "Ninja", "vendor": {"qt-project.org/Version": {"checksum": "YAsoBLt6QZBggacJefbW6uoClsY="}}}], "vendor": {"qt-project.org/Presets": {"checksum": "VChA1AxhS9oCsgVo42H+j5d8m84="}}}