#include "mq_client.h"
#include "mq_context.h"

#include <zmq.h>
#include <cerrno>

MqClient::MqClient() noexcept {
  ctx_ = mq::SharedContext();
  own_ctx_ = false;
}

MqClient::MqClient(void* ctx, bool own) noexcept {
  ctx_ = ctx ? ctx : mq::SharedContext();
  own_ctx_ = own && ctx != nullptr;
}

MqClient::~MqClient() {
  // 依次关闭 socket
  DisconnectReq();
  DisconnectSub();
  if (own_ctx_ && ctx_) {
    zmq_ctx_term(ctx_);
    ctx_ = nullptr;
  }
}

MqClient::MqClient(MqClient&& other) noexcept {
  ctx_ = other.ctx_;
  req_ = other.req_;
  req_connected_ = other.req_connected_;
  sub_ = other.sub_;
  sub_connected_ = other.sub_connected_;
  last_errno_ = other.last_errno_;
  own_ctx_ = other.own_ctx_;

  other.ctx_ = nullptr;
  other.req_ = nullptr;
  other.req_connected_ = false;
  other.sub_ = nullptr;
  other.sub_connected_ = false;
  other.last_errno_ = 0;
  other.own_ctx_ = false;
}

MqClient& MqClient::operator=(MqClient&& other) noexcept {
  if (this == &other)
    return *this;
  // 清理自身
  DisconnectReq();
  DisconnectSub();
  if (own_ctx_ && ctx_) { zmq_ctx_term(ctx_); }

  ctx_ = other.ctx_;
  req_ = other.req_;
  req_connected_ = other.req_connected_;
  sub_ = other.sub_;
  sub_connected_ = other.sub_connected_;
  last_errno_ = other.last_errno_;
  own_ctx_ = other.own_ctx_;

  other.ctx_ = nullptr;
  other.req_ = nullptr;
  other.req_connected_ = false;
  other.sub_ = nullptr;
  other.sub_connected_ = false;
  other.last_errno_ = 0;
  other.own_ctx_ = false;
  return *this;
}

bool MqClient::EnsureContext() {
  if (ctx_)
    return true;
  ctx_ = mq::SharedContext();
  own_ctx_ = false;
  return ctx_ != nullptr;
}

bool MqClient::EnsureReq() {
  if (!EnsureContext())
    return false;
  if (req_)
    return true;
  req_ = zmq_socket(ctx_, ZMQ_REQ);
  if (!req_) {
    SetErrorFromErrno();
    return false;
  }
  req_connected_ = false;
  return true;
}

bool MqClient::EnsureSub() {
  if (!EnsureContext())
    return false;
  if (sub_)
    return true;
  sub_ = zmq_socket(ctx_, ZMQ_SUB);
  if (!sub_) {
    SetErrorFromErrno();
    return false;
  }
  sub_connected_ = false;
  return true;
}

bool MqClient::ConnectReq(const char* endpoint) {
  ResetError();
  if (!endpoint || !*endpoint) {
    last_errno_ = EINVAL;
    return false;
  }
  if (!EnsureReq())
    return false;

  if (req_connected_) {
    zmq_close(req_);
    req_ = nullptr;
    req_connected_ = false;
    if (!EnsureReq())
      return false;
  }
  if (zmq_connect(req_, endpoint) != 0) {
    SetErrorFromErrno();
    return false;
  }
  req_connected_ = true;
  return true;
}

void MqClient::DisconnectReq() {
  if (req_) {
    zmq_close(req_);
    req_ = nullptr;
  }
  req_connected_ = false;
}

bool MqClient::ConnectSub(const char* endpoint) {
  ResetError();
  if (!endpoint || !*endpoint) {
    last_errno_ = EINVAL;
    return false;
  }
  if (!EnsureSub())
    return false;

  if (sub_connected_) {
    zmq_close(sub_);
    sub_ = nullptr;
    sub_connected_ = false;
    if (!EnsureSub())
      return false;
  }
  if (zmq_connect(sub_, endpoint) != 0) {
    SetErrorFromErrno();
    return false;
  }
  sub_connected_ = true;
  return true;
}

void MqClient::DisconnectSub() {
  if (sub_) {
    zmq_close(sub_);
    sub_ = nullptr;
  }
  sub_connected_ = false;
}

bool MqClient::SetReqSendTimeout(int ms) {
  ResetError();
  if (!EnsureReq())
    return false;
  if (zmq_setsockopt(req_, ZMQ_SNDTIMEO, &ms, sizeof(ms)) != 0) {
    SetErrorFromErrno();
    return false;
  }
  return true;
}

bool MqClient::SetReqRecvTimeout(int ms) {
  ResetError();
  if (!EnsureReq())
    return false;
  if (zmq_setsockopt(req_, ZMQ_RCVTIMEO, &ms, sizeof(ms)) != 0) {
    SetErrorFromErrno();
    return false;
  }
  return true;
}

bool MqClient::SetSubRecvTimeout(int ms) {
  ResetError();
  if (!EnsureSub())
    return false;
  if (zmq_setsockopt(sub_, ZMQ_RCVTIMEO, &ms, sizeof(ms)) != 0) {
    SetErrorFromErrno();
    return false;
  }
  return true;
}

bool MqClient::Request(const void* data, size_t size, std::string& reply,
                       int timeout_ms) {
  ResetError();
  if (!req_) {
    last_errno_ = EINVAL;
    return false;
  }

  int old_rcv = -1;
  size_t optlen = sizeof(old_rcv);
  bool changed = false;
  if (timeout_ms >= 0) {
    (void)zmq_getsockopt(req_, ZMQ_RCVTIMEO, &old_rcv, &optlen);
    if (zmq_setsockopt(req_, ZMQ_RCVTIMEO, &timeout_ms, sizeof(timeout_ms)) !=
        0) {
      SetErrorFromErrno();
      return false;
    }
    changed = true;
  }
  auto restore = [this, changed, old_rcv]() {
    if (changed && req_) {
      (void)zmq_setsockopt(req_, ZMQ_RCVTIMEO, &old_rcv, sizeof(old_rcv));
    }
  };

  if (zmq_send(req_, data, size, 0) == -1) {
    SetErrorFromErrno();
    restore();
    return false;
  }

  zmq_msg_t msg;
  if (zmq_msg_init(&msg) != 0) {
    SetErrorFromErrno();
    restore();
    return false;
  }
  int rc = zmq_msg_recv(&msg, req_, 0);
  if (rc == -1) {
    SetErrorFromErrno();
    zmq_msg_close(&msg);
    restore();
    return false;
  }

  const void* ptr = zmq_msg_data(&msg);
  size_t sz = zmq_msg_size(&msg);
  reply.assign(static_cast<const char*>(ptr), sz);
  zmq_msg_close(&msg);
  restore();
  return true;
}

bool MqClient::Subscribe(const void* topic, size_t size) {
  ResetError();
  if (!sub_) {
    last_errno_ = EINVAL;
    return false;
  }
  if (!topic && size != 0) {
    last_errno_ = EINVAL;
    return false;
  }
  if (zmq_setsockopt(sub_, ZMQ_SUBSCRIBE, topic, static_cast<int>(size)) != 0) {
    SetErrorFromErrno();
    return false;
  }
  return true;
}

bool MqClient::Unsubscribe(const void* topic, size_t size) {
  ResetError();
  if (!sub_) {
    last_errno_ = EINVAL;
    return false;
  }
  if (!topic && size != 0) {
    last_errno_ = EINVAL;
    return false;
  }
  if (zmq_setsockopt(sub_, ZMQ_UNSUBSCRIBE, topic, static_cast<int>(size)) !=
      0) {
    SetErrorFromErrno();
    return false;
  }
  return true;
}

bool MqClient::RecvSub(std::string& out, bool dont_wait) {
  ResetError();
  if (!sub_) {
    last_errno_ = EINVAL;
    return false;
  }

  zmq_msg_t msg;
  if (zmq_msg_init(&msg) != 0) {
    SetErrorFromErrno();
    return false;
  }
  int flags = dont_wait ? ZMQ_DONTWAIT : 0;
  int rc = zmq_msg_recv(&msg, sub_, flags);
  if (rc == -1) {
    SetErrorFromErrno();
    zmq_msg_close(&msg);
    return false;
  }
  const void* ptr = zmq_msg_data(&msg);
  size_t sz = zmq_msg_size(&msg);
  out.assign(static_cast<const char*>(ptr), sz);
  zmq_msg_close(&msg);
  return true;
}

int MqClient::Poll(int timeout_ms) {
  ResetError();
  zmq_pollitem_t items[2];
  int n = 0;
  if (sub_) {
    items[n].socket = sub_;
    items[n].fd = 0;
    items[n].events = ZMQ_POLLIN;
    items[n].revents = 0;
    ++n;
  }
  if (req_) {
    items[n].socket = req_;
    items[n].fd = 0;
    items[n].events = ZMQ_POLLIN;
    items[n].revents = 0;
    ++n;
  }
  if (n == 0)
    return 0;

  int rc = zmq_poll(items, n, timeout_ms);
  if (rc == -1) {
    SetErrorFromErrno();
    return 0;
  }

  int mask = None;
  int idx = 0;
  if (sub_) {
    if (items[idx].revents & ZMQ_POLLIN)
      mask |= SubIn;
    ++idx;
  }
  if (req_) {
    if (items[idx].revents & ZMQ_POLLIN)
      mask |= ReqIn;
  }
  return mask;
}

std::string MqClient::LastErrorMessage() const {
  if (last_errno_ == 0)
    return {};
  const char* s = zmq_strerror(last_errno_);
  return s ? std::string{s} : std::string{"Unknown error"};
}

void MqClient::SetErrorFromErrno() { last_errno_ = errno; }
