#ifndef AILAYOUT_MQ_NODE_H
#define AILAYOUT_MQ_NODE_H

#include <string>

class MqClient {
public:
  enum PollFlags { None = 0, SubIn = 1, ReqIn = 2 };

  MqClient() noexcept;
  explicit MqClient(void* ctx, bool own = false) noexcept;
  ~MqClient();

  MqClient(const MqClient&) = delete;
  MqClient& operator=(const MqClient&) = delete;
  MqClient(MqClient&& other) noexcept;
  MqClient& operator=(MqClient&& other) noexcept;

  bool ConnectReq(const char* endpoint);
  void DisconnectReq();
  bool HasReq() const { return req_ != nullptr && req_connected_; }

  bool ConnectSub(const char* endpoint);
  void DisconnectSub();
  bool HasSub() const { return sub_ != nullptr && sub_connected_; }

  bool SetReqSendTimeout(int ms);
  bool SetReqRecvTimeout(int ms);
  bool SetSubRecvTimeout(int ms);

  bool Request(const void* data, size_t size, std::string& reply,
               int timeout_ms = -1);

  bool Subscribe(const void* topic, size_t size);

  bool Subscribe(const std::string& topic) {
    return Subscribe(topic.data(), topic.size());
  }

  bool Unsubscribe(const void* topic, size_t size);

  bool Unsubscribe(const std::string& topic) {
    return Unsubscribe(topic.data(), topic.size());
  }

  bool RecvSub(std::string& out, bool dont_wait = false);

  int Poll(int timeout_ms);

  int LastErrorCode() const { return last_errno_; }
  std::string LastErrorMessage() const;

private:
  void ResetError() { last_errno_ = 0; }
  void SetErrorFromErrno();
  bool EnsureContext();
  bool EnsureReq();
  bool EnsureSub();

  void* ctx_ = nullptr;
  void* sub_ = nullptr;
  void* req_ = nullptr;

  int last_errno_ = 0;
  bool own_ctx_ = false;
  bool req_connected_ = false;
  bool sub_connected_ = false;

};

#endif // AILAYOUT_MQ_NODE_H