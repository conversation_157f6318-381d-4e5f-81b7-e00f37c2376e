#include "mq_context.h"

#include <atomic>
#include <mutex>
#include <zmq.h>

namespace mq {

static std::once_flag g_once;
static void* g_context = nullptr;
static std::atomic<int> g_io_threads{1};

void* SharedContext() noexcept {
  std::call_once(g_once, []() {
    g_context = zmq_ctx_new();
    if (g_context) {
      int threads = g_io_threads.load();
      if (threads < 1) {
        threads = 1;
      }
      zmq_ctx_set(g_context, ZMQ_IO_THREADS, threads);
    }
  });
  return g_context;
}

bool ConfigureCtxIoThreads(int threads) noexcept {
  if (threads < 1) {
    threads = 1;
  }
  if (g_context != nullptr) {
    return false;
  }
  g_io_threads.store(threads);
  return true;
}

} // namespace mq