#ifndef APP_UI_RULEUI_RULE_CONFIG_WIDGET_H
#define APP_UI_RULEUI_RULE_CONFIG_WIDGET_H

#include <QJsonObject>
#include <QListWidget>
#include <QMap>
#include <QTextEdit>
#include <QWidget>
#include <memory>

#include "agent/llm_rule_agent.h"
#include "rule_config_dialog.h"

class RuleConfigWidget : public QWidget {
  Q_OBJECT

public:
  RuleConfigWidget(QWidget* parent = nullptr);
  ~RuleConfigWidget();

private:
  QListWidget* list_widget_;
  QTextEdit* text_edit_;
  QPushButton* analysis_btn_;
  QPushButton* image_btn_;
  QPushButton* auto_layout_btn_;

  QString image_path_;

  QMap<QString, QPair<QString, std::function<ConfigDialogAPI*()>>> rule_map_;
  std::shared_ptr<Agent::LLMRuleAgent> llm_agent_;
  QJsonObject config_;

  void InitConfigList();
  void InitLLM();

private slots:
  void OnItemDoubleClicked(QListWidgetItem* item);
  void OnImageBtnClicked();
  void OnAnalysisBtnClicked();
  void OnAutoLayoutBtnClicked();
};

#endif