#include "project_widget.h"
#include <QVBoxLayout>

ProjectWidget::ProjectWidget(QWidget* parent) : QWidget(parent) {
  tree_widget_ = new QTreeWidget(this);
  tree_widget_->setHeaderLabels(QStringList() << "Component");

  auto layout = new QVBoxLayout(this);
  layout->setContentsMargins(0, 0, 0, 0);
  layout->addWidget(tree_widget_);
}

void ProjectWidget::AddComponents(const QStringList& comp_names) {
  for (auto& comp_name : comp_names) {
    QTreeWidgetItem* item = new QTreeWidgetItem(tree_widget_);
    item->setText(0, comp_name);
  }
}

void ProjectWidget::ClearComponents() {
  tree_widget_->clear();
}
