#ifndef APP_RULE_CONFIG_DIALOG_H_
#define APP_RULE_CONFIG_DIALOG_H_

#include <tuple>

#include <QDialog>
#include <QDialogButtonBox>
#include <QTableWidget>
#include "agent/llm_rule_agent.h"

class ConfigDialogAPI {
public:
  virtual ~ConfigDialogAPI() = default;
  virtual void InitConfig(const std::shared_ptr<Agent::LLMRuleAgent>& agent,
                          const QJsonObject& value) = 0;
  virtual QJsonObject GetConfig() = 0;
};

class NearConfigDialog : public QDialog, public ConfigDialogAPI {
  Q_OBJECT

public:
  NearConfigDialog(QString title = "Near Config", QWidget* parent = 0);
  ~NearConfigDialog();

  void InitConfig(const std::shared_ptr<Agent::LLMRuleAgent>& agent,
                  const QJsonObject& value) override;
  QJsonObject GetConfig() override;

private:
  std::shared_ptr<Agent::LLMRuleAgent> agent_;
  QString image_path_;

  QVector<std::tuple<QString, QString, double>> config_list_;
  QTableWidget* table_widget_;
  QPushButton* llm_btn_;
  QPushButton* add_btn_;
  QPushButton* del_btn_;
  QDialogButtonBox* button_box_;

  void UpdateUI();
  void InitUI();

private slots:
  void OnAddRow();
  void OnDelRow();
  void OnLLM();
};

#endif