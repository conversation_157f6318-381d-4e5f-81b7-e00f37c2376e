#include "log_widget.h"

#include <QVBoxLayout>

LogWidget::LogWidget(QWidget* parent) : QWidget(parent) {
  setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
  log_text_edit_ = new QTextEdit(this);

  auto* layout = new QVBoxLayout(this);
  layout->setContentsMargins(0, 0, 0, 0);  // 去掉边距
  layout->addWidget(log_text_edit_);
}

void LogWidget::AddLog(const QString& log) {
  log_text_edit_->append(log + '\n');
}
