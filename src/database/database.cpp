#include "database.h"
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>

#include "component.h"

namespace DataBase {
const QString PcbNameKey = "PcbName";
const QString ComponentsKey = "Components";
const QString DecalNameKey = "decalName";
const QString PartTypeKey = "PartType";
const QString GluidKey = "IsGluid";
const QString PosXKey = "PosX";
const QString PosYKey = "PosY";
const QString OrientationKey = "Orientation";
const QString WidthKey = "PickBoxWidth";
const QString HeightKey = "PickBoxHeight";
const QString PinsKey = "Pins";
const QString NetsKey = "Nets";
const QString PinPosXKey = "pinPosX";
const QString PinPosYKey = "pinPosY";

Database::Database(QObject* parent) : QObject(parent) {}

bool Database::InitDB(const QString& path) {
  QFile file(path);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
    return false;
  }

  QByteArray data = file.readAll();
  file.close();

  QJsonParseError error;
  QJsonDocument doc = QJsonDocument::fromJson(data, &error);
  if (doc.isNull()) {
    return false;
  }

  QJsonObject root = doc.object();
  if (!root.contains(PcbNameKey)) {
    return false;
  }
  pcb_name_ = root[PcbNameKey].toString();

  auto components = root[ComponentsKey].toObject();
  for (auto& comp_name : components.keys()) {
    auto comp = components[comp_name].toObject();
    QString decal_name;
    if (comp.contains(DecalNameKey)) {
      decal_name = comp[DecalNameKey].toString();
    } else if (comp.contains("decalName")) {
      decal_name = comp["decalName"].toString();
    }
    auto part_type = comp[PartTypeKey].toString();
    auto is_gluid = comp[GluidKey].toBool();
    auto layerStr = comp["Layer"].toString();
    Layer layer;
    if (layerStr == "Top" || layerStr == "top") {
      layer = Layer::Top;
    } else if (layerStr == "Bottom" || layerStr == "bottom") {
      layer = Layer::Bottom;
    }
    auto pos_x = comp[PosXKey].toDouble();
    auto pos_y = comp[PosYKey].toDouble();
    auto orientation = comp[OrientationKey].toDouble();
    auto width = comp[WidthKey].toDouble();
    auto height = comp[HeightKey].toDouble();

    QPolygonF polygon = CalcPolygon(QPointF(pos_x, pos_y), width, height);
    auto compPtr = std::make_shared<Component>(
        comp_name, decal_name, part_type, layer, is_gluid,
        QPointF(pos_x, pos_y), orientation, polygon);
    components_[comp_name] = compPtr;

    auto pins = comp[PinsKey].toObject();
    for (auto& pin_name : pins.keys()) {
      auto pin = pins[pin_name].toObject();
      auto pin_pos_x = pin[PinPosXKey].toDouble();
      auto pin_pos_y = pin[PinPosYKey].toDouble();

      auto padPtr =
          std::make_shared<Pad>(pin_name, QPointF(pin_pos_x, pin_pos_y));
      padPtr->component = compPtr;
      pads_[pin_name] = padPtr;
      compPtr->AddPad(padPtr);
    }
  }

  auto nets = root[NetsKey].toObject();
  for (auto& net_name : nets.keys()) {
    auto net = nets[net_name].toObject();
    auto netPtr = std::make_shared<Net>(net_name);
    for (auto& pad_name : net.keys()) {
      auto padPtr = pads_[pad_name];
      if (padPtr) {
        padPtr->net = netPtr;
        netPtr->AddPad(padPtr);
      }
    }
    nets_[net_name] = netPtr;
  }

  emit SigInitDB();

  return true;
}

QPolygonF Database::CalcPolygon(const QPointF& pos, double width,
                                double height) {
  QPolygonF polygon;
  polygon << QPointF(pos.x() - width / 2, pos.y() - height / 2)
          << QPointF(pos.x() - width / 2, pos.y() + height / 2)
          << QPointF(pos.x() + width / 2, pos.y() + height / 2)
          << QPointF(pos.x() + width / 2, pos.y() - height / 2)
          << QPointF(pos.x() - width / 2, pos.y() - height / 2);

  return polygon;
}

void Database::MoveComponent(const QString& comp_name, const QPointF& pos) {
  if (components_.contains(comp_name)) {
    auto compPtr = components_[comp_name];
    if (compPtr && compPtr->GetPosition() != pos) {
      compPtr->Move(pos);
    }
  }
}

void Database::RotateComponent(const QString& comp_name, double angle) {
  if (components_.contains(comp_name)) {
    auto compPtr = components_[comp_name];
    if (compPtr && qFuzzyCompare(angle, 0.0)) {
      compPtr->Rotate(angle);
    }
  }
}

void Database::FlipComponent(const QString& comp_name, bool is_top) {
  if (components_.contains(comp_name)) {
    auto compPtr = components_[comp_name];
    if (compPtr) {
      compPtr->Flip();
    }
  }
}

const QMap<QString, std::shared_ptr<Component>>& Database::GetComponents()
    const {
  return components_;
}

bool Database::UpdateDB(const QString& data) {
  emit SigUpdateDB();

  return true;
}

void Database::ClearDB() {
  components_.clear();
  pads_.clear();
  nets_.clear();
}

}  // namespace DataBase