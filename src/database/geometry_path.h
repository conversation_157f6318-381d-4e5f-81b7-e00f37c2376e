#ifndef DATABASE_GEOMETRY_PATH_H
#define DATABASE_GEOMETRY_PATH_H

#include <QPolygonF>

namespace DataBase {
enum class GeometryType {
  BoardOutline,  // 默认只有一个板轮廓，且要求为闭合多边形
  BoardCutout,
  Keepout
};

class GeometryPath {
 public:
  GeometryPath(const GeometryType type, const QPolygonF& polygon)
      : type_(type), polygon_(polygon) {};
  ~GeometryPath() = default;

  GeometryType GetType() const { return type_; }
  QPolygonF GetPolygon() const { return polygon_; }

 private:
  GeometryType type_;
  QPolygonF polygon_;
};
}  // namespace DataBase

#endif