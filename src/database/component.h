#ifndef DATA_BASE_COMPONENT_H
#define DATA_BASE_COMPONENT_H

#include "pad.h"

namespace DataBase {
class Component {
 public:
  Component(const QString& name, const QString& decalName,
            const QString& partType, Layer layer, bool isGluid,
            const QPointF& position, double rotation, const QPolygonF& polygon)
      : name_(name),
        decal_name_(decalName),
        part_type_(partType),
        layer_(layer),
        is_glued_(isGluid),
        position_(position),
        rotation_(rotation),
        polygon_(polygon) {};

  ~Component() = default;

  QString GetName() const { return name_; }
  QString GetDecalName() const { return decal_name_; }
  QString GetPartType() const { return part_type_; }
  Layer GetLayer() const { return layer_; }
  bool IsGlued() const { return is_glued_; }
  QPointF GetPosition() const { return position_; }
  double GetRotation() const { return rotation_; }
  QPolygonF GetPolygon() const { return polygon_; }
  void AddPad(std::shared_ptr<Pad> pad) { pads_.append(pad); }
  QVector<std::shared_ptr<Pad>> GetPads() const { return pads_; }

  void Rotate(double angle) {
    QTransform transform;
    transform.translate(position_.x(), position_.y());
    transform.rotate(angle);
    transform.translate(-position_.x(), -position_.y());
    polygon_ = transform.map(polygon_);
    CheckRotattion(rotation_, angle);

    for (auto& pad : pads_) {
      pad->position = transform.map(pad->position);
      CheckRotattion(pad->rotation, angle);
    }
  }

  void Move(const QPointF& pos) {
    const auto offset = position_ - pos;
    position_ = pos;
    polygon_ = polygon_.translated(offset);
    for (auto& pad : pads_) {
      pad->position = pad->position + offset;
    }
  }

  void Flip() {}

 private:
  QString name_;
  QString decal_name_;
  QString part_type_;
  Layer layer_;
  bool is_glued_;
  QPointF position_;
  double rotation_;
  QPolygonF polygon_;

  QVector<std::shared_ptr<Pad>> pads_;

  void CheckRotattion(double& rotation, const double angle) {
    rotation += angle;
    if (rotation >= 360.0) {
      rotation -= 360.0;
    } else if (rotation < 0.0) {
      rotation += 360.0;
    }
  }
};
}  // namespace DataBase

#endif