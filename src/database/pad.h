#ifndef DATA_BASE_PAD_H
#define DATA_BASE_PAD_H

#include <QPointF>
#include <QPolygonF>
#include <QString>
#include <QTransform>
#include <memory>

#include "base_type.h"

namespace DataBase {
class Component;
class Net;
struct Pad {
  QString name;
  QPointF position;
  double rotation;
  QPolygonF polygon;
  bool is_smd;

  std::weak_ptr<Component> component;
  std::weak_ptr<Net> net;

  Pad(const QString& name, const QPointF& pos)
      : name(name), position(pos), rotation(0), is_smd(false) {
    polygon = QPolygonF({QPointF(-3, -3), QPointF(3, -3), QPointF(3, 3),
                         QPointF(-3, 3), QPointF(-3, -3)});
  }

  QSizeF GetSize() const { return QSizeF(6, 6); }
};
}  // namespace DataBase

#endif