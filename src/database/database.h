#ifndef DATABASE_DATABASE_H
#define DATABASE_DATABASE_H

#include <QMap>
#include <QObject>

#include "component.h"
#include "geometry_path.h"
#include "net.h"
#include "pad.h"

namespace DataBase {
class Database : public QObject {
  Q_OBJECT

 public:
  explicit Database(QObject* parent = nullptr);
  ~Database() = default;

  Database(const Database&) = delete;
  Database& operator=(const Database&) = delete;

  /*
        * @brief 初始化数据库
        * @param path 数据库文件路径
        * @return true 初始化成功
        * @return false 初始化失败
        */
  bool InitDB(const QString& path);

  /*
        * @brief 更新数据库
        * @param data 数据库数据
        * @return true 更新成功
        * @return false 更新失败
        */
  bool UpdateDB(const QString& data);

  /*
        * @brief 清除数据库
        */
  void ClearDB();

  /*
        * @brief 移动器件
        * @param comp_name 器件名称
        * @param pos 器件位置
        */
  void MoveComponent(const QString& comp_name, const QPointF& pos);

  /*
        * @brief 旋转器件
        * @param comp_name 器件名称
        * @param angle 旋转角度
        */
  void RotateComponent(const QString& comp_name, double angle);

  /*
        * @brief 器件翻面
        * @param comp_name 器件名称
        * @param is_top true : 设置为Top面， false : 设置为Bottom面
        */
  void FlipComponent(const QString& comp_name, bool is_top = true);

  /*
        * @brief 获取器件列表
        * @return
        */
  const QMap<QString, std::shared_ptr<Component>>& GetComponents() const;

 signals:
  void SigInitDB();
  void SigUpdateDB();

 private:
  QString pcb_name_;
  QMap<QString, std::shared_ptr<Pad>> pads_;
  QMap<QString, std::shared_ptr<Component>> components_;
  QMap<QString, std::shared_ptr<Net>> nets_;

  QVector<GeometryPath> keepout_;
  QVector<GeometryPath> board_outline_;
  QVector<GeometryPath> board_cutout_;

  QPolygonF CalcPolygon(const QPointF& pos, double width, double height);
};
}  // namespace DataBase

#endif  // DATABASE_DATABASE_H