#include "Item.h"
#include <QPainter>

namespace Editor {
Item::Item(DataBase::ItemType type, DataBase::Layer layer, const QString& name,
           QGraphicsItem* parent)
    : QObject(),
      QGraphicsPathItem(parent),
      type_(type),
      layer_(layer),
      name_(name) {
  this->setFlag(QGraphicsItem::ItemIsMovable);
  this->setFlag(QGraphicsItem::ItemIsSelectable);
}

Item::Item(DataBase::ItemType type, DataBase::Layer layer, const QString& name,
           const QPainterPath& path, QGraphicsItem* parent)
    : QObject(),
      QGraphicsPathItem(path, parent),
      type_(type),
      layer_(layer),
      name_(name) {
  this->setFlag(QGraphicsItem::ItemIsMovable);
  this->setFlag(QGraphicsItem::ItemIsSelectable);
}

Item::~Item() = default;

void Item::paint(QPainter* painter, const QStyleOptionGraphicsItem* option,
                 QWidget* widget) {
  setBrush(brush_map_.GetBrush(layer_, type_));
  QGraphicsPathItem::paint(painter, option, widget);
}

QVariant Item::itemChange(GraphicsItemChange change, const QVariant& value) {
  if (change == ItemPositionHasChanged) {
    emit SigPositionChanged(name_, value.toPointF());
  } else if (change == ItemSelectedHasChanged) {
    emit SigSelectedChanged(name_, value.toBool());
  }

  return QGraphicsPathItem::itemChange(change, value);
}
}  // namespace Editor