#ifndef EDITOR_EDITOR_H
#define EDITOR_EDITOR_H

#include <memory>

#include <QGraphicsView>
#include <QVector>
#include <QMap>

#include "Item.h"
#include "database/component.h"

namespace Editor {

class Editor : public QGraphicsView {
  Q_OBJECT

public:
  Editor(QWidget* parent = nullptr);
  ~Editor();

  Item* AddItem(const std::shared_ptr<DataBase::Component>& comp);

  void Clear();

  void UpdateAllItems(
      const QMap<QString, std::shared_ptr<DataBase::Component>>& comp_map);

  void ResizeView();

signals:
  void SigMouseMove(QPointF pos);

protected:
  void resizeEvent(QResizeEvent* event) override;
  void wheelEvent(QWheelEvent* event) override;
  void mouseMoveEvent(QMouseEvent* event) override;

private:
  QGraphicsScene* scene_;
  QMap<QString, Item*> items_;

  void AddTextItem(QGraphicsItem* parent, const QString& text,
                   const QPointF& pos);
};
}

#endif //EDITOR_EDITOR_H