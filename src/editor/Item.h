#ifndef EDITOR_ITEM_H
#define EDITOR_ITEM_H

#include <QGraphicsPathItem>

#include "brush_map.h"

namespace Editor {
    class Item : public QObject, public QGraphicsPathItem {
        Q_OBJECT
        
    public:
        Item(DataBase::ItemType type, DataBase::Layer layer, const QString& name, QGraphicsItem* parent = nullptr);
        Item(DataBase::ItemType type, DataBase::Layer layer, const QString& name, const QPainterPath& path, QGraphicsItem* parent = nullptr);
        ~Item();

        virtual void paint(QPainter* painter, const QStyleOptionGraphicsItem* option, QWidget* widget = nullptr) override;

		void SetComAngle(double angle) { com_angle_ = angle; }
        double GetComAngle() const { return com_angle_; }
        QString GetName() const { return name_; }
		DataBase::Layer GetLayer() const { return layer_; }
		void SetLayer(DataBase::Layer layer) { layer_ = layer; }

    signals:
        void SigPositionChanged(const QString name, const QPointF pos);
        void SigSelectedChanged(const QString name, bool is_selected);

    protected:
        QVariant itemChange(GraphicsItemChange change, const QVariant& value) override;

    private:
        DataBase::ItemType type_;
        DataBase::Layer layer_;
		double com_angle_ = 0;

        QString name_;

        BrushMap brush_map_;
    };
}

#endif //EDITOR_ITEM_H