/* 主窗口背景 */
QMainWindow {
    background-color: #2b2b2b;
}

/* 菜单栏样式 */
QMenuBar {
    background-color: #3c3f41;
    color: #ffffff;
    border-bottom: 1px solid #555555;
}

QMenuBar::item {
    background: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background: #4b6eaf;
}

QMenuBar::item:pressed {
    background: #3a5a8a;
}

/* 菜单样式 */
QMenu {
    background-color: #3c3f41;
    color: #ffffff;
    border: 1px solid #555555;
}

QMenu::item {
    padding: 4px 20px;
}

QMenu::item:selected {
    background-color: #4b6eaf;
}

QMenu::separator {
    height: 1px;
    background: #555555;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #3c3f41;
    color: #ffffff;
    border-top: 1px solid #555555;
}

QStatusBar QLabel {
    padding: 2px;
}

/* 停靠窗口样式 */
QDockWidget {
    border: 1px solid #555555;
}

QDockWidget::title {
    background: #3c3f41;
    padding: 4px;
    border-bottom: 1px solid #555555;
    font-weight: bold;
    color: #ffffff;
}

/* 停靠窗口关闭按钮 */
QDockWidget::close-button, QDockWidget::float-button {
    background: transparent;
    border: none;
    icon-size: 16px;
}

QDockWidget::close-button:hover, QDockWidget::float-button:hover {
    background: #4b6eaf;
    border-radius: 2px;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background: #3c3f41;
    width: 15px;
    border-radius: 4px;
}

QScrollBar::handle:vertical {
    background: #5a5d5e;
    border-radius: 4px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #4b6eaf;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
    background: none;
}

QScrollBar:horizontal {
    background: #3c3f41;
    height: 15px;
    border-radius: 4px;
}

QScrollBar::handle:horizontal {
    background: #5a5d5e;
    border-radius: 4px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background: #4b6eaf;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
    background: none;
}

/* 图形视图背景 */
QGraphicsView {
    background-color: #1e1e1e;
    border: none;
}

/* 对话框样式 */
QFileDialog {
    background-color: #2b2b2b;
}

QFileDialog QListView, QFileDialog QTreeView {
    background-color: #2b2b2b;
    color: #ffffff;
    selection-background-color: #4b6eaf;
}
